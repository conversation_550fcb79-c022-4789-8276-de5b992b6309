"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/results/entry/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/results/entry/page.tsx":
/*!**************************************************!*\
  !*** ./src/app/dashboard/results/entry/page.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ResultsEntryPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Eye!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Eye!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Eye!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst BAND_SCORES = [\n    1.0,\n    1.5,\n    2.0,\n    2.5,\n    3.0,\n    3.5,\n    4.0,\n    4.5,\n    5.0,\n    5.5,\n    6.0,\n    6.5,\n    7.0,\n    7.5,\n    8.0,\n    8.5,\n    9.0\n];\nfunction ResultsEntryPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [candidates, setCandidates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [bandScores, setBandScores] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [successMessage, setSuccessMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ResultsEntryPage.useEffect\": ()=>{\n            fetchCandidates();\n        }\n    }[\"ResultsEntryPage.useEffect\"], []);\n    const fetchCandidates = async ()=>{\n        try {\n            const response = await fetch('/api/checker/candidates?includeResults=true');\n            if (response.ok) {\n                var _data_candidates;\n                const data = await response.json();\n                setCandidates(data.candidates || []);\n                // Initialize band scores from existing results\n                const initialScores = {};\n                (_data_candidates = data.candidates) === null || _data_candidates === void 0 ? void 0 : _data_candidates.forEach((candidate)=>{\n                    if (candidate.result) {\n                        initialScores[candidate.id] = {\n                            listening: candidate.result.listeningBandScore,\n                            reading: candidate.result.readingBandScore,\n                            writingTask1: null,\n                            writingTask2: null,\n                            speaking: candidate.result.speakingBandScore\n                        };\n                    }\n                });\n                setBandScores(initialScores);\n            } else {\n                const errorData = await response.json();\n                setError(errorData.error || 'Failed to fetch candidates');\n            }\n        } catch (error) {\n            console.error('Error fetching candidates:', error);\n            setError('An error occurred while fetching candidates');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const updateBandScore = (candidateId, skill, score)=>{\n        setBandScores((prev)=>({\n                ...prev,\n                [candidateId]: {\n                    listening: null,\n                    reading: null,\n                    writingTask1: null,\n                    writingTask2: null,\n                    speaking: null,\n                    ...prev[candidateId],\n                    [skill]: score\n                }\n            }));\n    };\n    const calculateOverallBandScore = (scores)=>{\n        const { listening, reading, writingTask1, writingTask2, speaking } = scores;\n        // Calculate writing band score from tasks\n        let writingBand = null;\n        if (writingTask1 !== null && writingTask2 !== null) {\n            writingBand = (writingTask1 + writingTask2) / 2;\n        }\n        const validScores = [\n            listening,\n            reading,\n            writingBand,\n            speaking\n        ].filter((score)=>score !== null);\n        if (validScores.length === 4) {\n            const average = validScores.reduce((sum, score)=>sum + score, 0) / 4;\n            return Math.round(average * 2) / 2; // Round to nearest 0.5\n        }\n        return null;\n    };\n    const saveBandScore = async (candidateId, skill)=>{\n        const scores = bandScores[candidateId];\n        if (!scores) return;\n        setIsSaving(true);\n        try {\n            var _candidate_result;\n            const candidate = candidates.find((c)=>c.id === candidateId);\n            if (!candidate) return;\n            const overallBandScore = calculateOverallBandScore(scores);\n            // Prepare the data for API\n            const resultData = {\n                candidateId,\n                listeningBandScore: scores.listening,\n                readingBandScore: scores.reading,\n                writingTask1Score: scores.writingTask1,\n                writingTask2Score: scores.writingTask2,\n                writingBandScore: scores.writingTask1 && scores.writingTask2 ? (scores.writingTask1 + scores.writingTask2) / 2 : null,\n                speakingBandScore: scores.speaking,\n                overallBandScore,\n                status: 'completed'\n            };\n            const url = candidate.hasResult ? \"/api/checker/results/\".concat((_candidate_result = candidate.result) === null || _candidate_result === void 0 ? void 0 : _candidate_result.id) : '/api/admin/results';\n            const method = candidate.hasResult ? 'PUT' : 'POST';\n            const response = await fetch(url, {\n                method,\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(resultData)\n            });\n            if (response.ok) {\n                setSuccessMessage(\"\".concat(skill, \" score saved for \").concat(candidate.candidateNumber));\n                setTimeout(()=>setSuccessMessage(''), 3000);\n                await fetchCandidates(); // Refresh data\n            } else {\n                const errorData = await response.json();\n                setError(errorData.error || 'Failed to save score');\n            }\n        } catch (error) {\n            console.error('Error saving score:', error);\n            setError('An error occurred while saving the score');\n        } finally{\n            setIsSaving(false);\n        }\n    };\n    const getBandScoreColor = (score)=>{\n        if (score === null) return 'bg-gray-100 text-gray-400';\n        if (score >= 8.5) return 'bg-green-100 text-green-800';\n        if (score >= 7.0) return 'bg-blue-100 text-blue-800';\n        if (score >= 6.0) return 'bg-yellow-100 text-yellow-800';\n        if (score >= 5.0) return 'bg-orange-100 text-orange-800';\n        return 'bg-red-100 text-red-800';\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                lineNumber: 186,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n            lineNumber: 185,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: \"Results Entry\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Quickly assign band scores to candidates\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"h-4 w-4 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 11\n                    }, this),\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                lineNumber: 201,\n                columnNumber: 9\n            }, this),\n            successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-md text-sm flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"h-4 w-4 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 11\n                    }, this),\n                    successMessage\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                lineNumber: 208,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow rounded-lg overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"overflow-x-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: \"min-w-full divide-y divide-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                className: \"bg-gray-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Candidate\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Listening\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Reading\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Writing Task 1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Writing Task 2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Speaking\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                className: \"bg-white divide-y divide-gray-200\",\n                                children: candidates.map((candidate)=>{\n                                    const scores = bandScores[candidate.id] || {};\n                                    const overallScore = calculateOverallBandScore(scores);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        className: \"hover:bg-gray-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-shrink-0 h-10 w-10\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium text-blue-800\",\n                                                                    children: candidate.candidateNumber\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                                    lineNumber: 254,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                                lineNumber: 253,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                            lineNumber: 252,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"ml-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium text-gray-900\",\n                                                                    children: candidate.fullName\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                                    lineNumber: 260,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: [\n                                                                        \"Test Date: \",\n                                                                        new Date(candidate.testDate).toLocaleDateString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                                    lineNumber: 263,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                overallScore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-400\",\n                                                                    children: [\n                                                                        \"Overall: \",\n                                                                        overallScore\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                                    lineNumber: 267,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BandScoreSelector, {\n                                                    value: scores.listening,\n                                                    onChange: (score)=>updateBandScore(candidate.id, 'listening', score),\n                                                    onSave: ()=>saveBandScore(candidate.id, 'listening'),\n                                                    disabled: isSaving\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BandScoreSelector, {\n                                                    value: scores.reading,\n                                                    onChange: (score)=>updateBandScore(candidate.id, 'reading', score),\n                                                    onSave: ()=>saveBandScore(candidate.id, 'reading'),\n                                                    disabled: isSaving\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BandScoreSelector, {\n                                                    value: scores.writingTask1,\n                                                    onChange: (score)=>updateBandScore(candidate.id, 'writingTask1', score),\n                                                    onSave: ()=>saveBandScore(candidate.id, 'writingTask1'),\n                                                    disabled: isSaving\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BandScoreSelector, {\n                                                    value: scores.writingTask2,\n                                                    onChange: (score)=>updateBandScore(candidate.id, 'writingTask2', score),\n                                                    onSave: ()=>saveBandScore(candidate.id, 'writingTask2'),\n                                                    disabled: isSaving\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BandScoreSelector, {\n                                                    value: scores.speaking,\n                                                    onChange: (score)=>updateBandScore(candidate.id, 'speaking', score),\n                                                    onSave: ()=>saveBandScore(candidate.id, 'speaking'),\n                                                    disabled: isSaving\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        var _candidate_result;\n                                                        return router.push(\"/dashboard/results/\".concat(((_candidate_result = candidate.result) === null || _candidate_result === void 0 ? void 0 : _candidate_result.id) || candidate.id));\n                                                    },\n                                                    className: \"text-blue-600 hover:text-blue-900 text-sm\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, candidate.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                lineNumber: 215,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n        lineNumber: 192,\n        columnNumber: 5\n    }, this);\n}\n_s(ResultsEntryPage, \"HlSUETyJrtCLVDhILNNP2yWpyes=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = ResultsEntryPage;\nfunction BandScoreSelector(param) {\n    let { value, onChange, onSave, disabled } = param;\n    _s1();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const getBandScoreColor = (score)=>{\n        if (score === null) return 'bg-gray-100 text-gray-400 border-gray-200';\n        if (score >= 8.5) return 'bg-green-100 text-green-800 border-green-200';\n        if (score >= 7.0) return 'bg-blue-100 text-blue-800 border-blue-200';\n        if (score >= 6.0) return 'bg-yellow-100 text-yellow-800 border-yellow-200';\n        if (score >= 5.0) return 'bg-orange-100 text-orange-800 border-orange-200';\n        return 'bg-red-100 text-red-800 border-red-200';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"inline-flex items-center px-3 py-2 border rounded-md text-sm font-medium \".concat(getBandScoreColor(value), \" hover:opacity-80 focus:outline-none focus:ring-2 focus:ring-blue-500\"),\n                disabled: disabled,\n                children: value !== null ? \"Band \".concat(value) : 'Pending'\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                lineNumber: 366,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute z-10 mt-1 w-32 bg-white border border-gray-200 rounded-md shadow-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"py-1 max-h-48 overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                onChange(null);\n                                setIsOpen(false);\n                            },\n                            className: \"block w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-100\",\n                            children: \"Pending\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                            lineNumber: 377,\n                            columnNumber: 13\n                        }, this),\n                        BAND_SCORES.map((score)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    onChange(score);\n                                    setIsOpen(false);\n                                    setTimeout(onSave, 100); // Auto-save after selection\n                                },\n                                className: \"block w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-100\",\n                                children: [\n                                    \"Band \",\n                                    score\n                                ]\n                            }, score, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 15\n                            }, this))\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                    lineNumber: 376,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                lineNumber: 375,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n        lineNumber: 365,\n        columnNumber: 5\n    }, this);\n}\n_s1(BandScoreSelector, \"+sus0Lb0ewKHdwiUhiTAJFoFyQ0=\");\n_c1 = BandScoreSelector;\nvar _c, _c1;\n$RefreshReg$(_c, \"ResultsEntryPage\");\n$RefreshReg$(_c1, \"BandScoreSelector\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZGFzaGJvYXJkL3Jlc3VsdHMvZW50cnkvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUU0QztBQUNBO0FBQ2lCO0FBK0I3RCxNQUFNTSxjQUFjO0lBQUM7SUFBSztJQUFLO0lBQUs7SUFBSztJQUFLO0lBQUs7SUFBSztJQUFLO0lBQUs7SUFBSztJQUFLO0lBQUs7SUFBSztJQUFLO0lBQUs7SUFBSztDQUFJO0FBRTFGLFNBQVNDOztJQUN0QixNQUFNQyxTQUFTTiwwREFBU0E7SUFDeEIsTUFBTSxDQUFDTyxZQUFZQyxjQUFjLEdBQUdWLCtDQUFRQSxDQUFjLEVBQUU7SUFDNUQsTUFBTSxDQUFDVyxZQUFZQyxjQUFjLEdBQUdaLCtDQUFRQSxDQUFhLENBQUM7SUFDMUQsTUFBTSxDQUFDYSxXQUFXQyxhQUFhLEdBQUdkLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ2UsVUFBVUMsWUFBWSxHQUFHaEIsK0NBQVFBLENBQUM7SUFDekMsTUFBTSxDQUFDaUIsT0FBT0MsU0FBUyxHQUFHbEIsK0NBQVFBLENBQUM7SUFDbkMsTUFBTSxDQUFDbUIsZ0JBQWdCQyxrQkFBa0IsR0FBR3BCLCtDQUFRQSxDQUFDO0lBRXJEQyxnREFBU0E7c0NBQUM7WUFDUm9CO1FBQ0Y7cUNBQUcsRUFBRTtJQUVMLE1BQU1BLGtCQUFrQjtRQUN0QixJQUFJO1lBQ0YsTUFBTUMsV0FBVyxNQUFNQyxNQUFNO1lBQzdCLElBQUlELFNBQVNFLEVBQUUsRUFBRTtvQkFNZkM7Z0JBTEEsTUFBTUEsT0FBTyxNQUFNSCxTQUFTSSxJQUFJO2dCQUNoQ2hCLGNBQWNlLEtBQUtoQixVQUFVLElBQUksRUFBRTtnQkFFbkMsK0NBQStDO2dCQUMvQyxNQUFNa0IsZ0JBQTRCLENBQUM7aUJBQ25DRixtQkFBQUEsS0FBS2hCLFVBQVUsY0FBZmdCLHVDQUFBQSxpQkFBaUJHLE9BQU8sQ0FBQyxDQUFDQztvQkFDeEIsSUFBSUEsVUFBVUMsTUFBTSxFQUFFO3dCQUNwQkgsYUFBYSxDQUFDRSxVQUFVRSxFQUFFLENBQUMsR0FBRzs0QkFDNUJDLFdBQVdILFVBQVVDLE1BQU0sQ0FBQ0csa0JBQWtCOzRCQUM5Q0MsU0FBU0wsVUFBVUMsTUFBTSxDQUFDSyxnQkFBZ0I7NEJBQzFDQyxjQUFjOzRCQUNkQyxjQUFjOzRCQUNkQyxVQUFVVCxVQUFVQyxNQUFNLENBQUNTLGlCQUFpQjt3QkFDOUM7b0JBQ0Y7Z0JBQ0Y7Z0JBQ0EzQixjQUFjZTtZQUNoQixPQUFPO2dCQUNMLE1BQU1hLFlBQVksTUFBTWxCLFNBQVNJLElBQUk7Z0JBQ3JDUixTQUFTc0IsVUFBVXZCLEtBQUssSUFBSTtZQUM5QjtRQUNGLEVBQUUsT0FBT0EsT0FBTztZQUNkd0IsUUFBUXhCLEtBQUssQ0FBQyw4QkFBOEJBO1lBQzVDQyxTQUFTO1FBQ1gsU0FBVTtZQUNSSixhQUFhO1FBQ2Y7SUFDRjtJQUVBLE1BQU00QixrQkFBa0IsQ0FBQ0MsYUFBcUJDLE9BQWlDQztRQUM3RWpDLGNBQWNrQyxDQUFBQSxPQUFTO2dCQUNyQixHQUFHQSxJQUFJO2dCQUNQLENBQUNILFlBQVksRUFBRTtvQkFDYlgsV0FBVztvQkFDWEUsU0FBUztvQkFDVEUsY0FBYztvQkFDZEMsY0FBYztvQkFDZEMsVUFBVTtvQkFDVixHQUFHUSxJQUFJLENBQUNILFlBQVk7b0JBQ3BCLENBQUNDLE1BQU0sRUFBRUM7Z0JBQ1g7WUFDRjtJQUNGO0lBRUEsTUFBTUUsNEJBQTRCLENBQUNDO1FBQ2pDLE1BQU0sRUFBRWhCLFNBQVMsRUFBRUUsT0FBTyxFQUFFRSxZQUFZLEVBQUVDLFlBQVksRUFBRUMsUUFBUSxFQUFFLEdBQUdVO1FBRXJFLDBDQUEwQztRQUMxQyxJQUFJQyxjQUFjO1FBQ2xCLElBQUliLGlCQUFpQixRQUFRQyxpQkFBaUIsTUFBTTtZQUNsRFksY0FBYyxDQUFDYixlQUFlQyxZQUFXLElBQUs7UUFDaEQ7UUFFQSxNQUFNYSxjQUFjO1lBQUNsQjtZQUFXRTtZQUFTZTtZQUFhWDtTQUFTLENBQUNhLE1BQU0sQ0FBQ04sQ0FBQUEsUUFBU0EsVUFBVTtRQUUxRixJQUFJSyxZQUFZRSxNQUFNLEtBQUssR0FBRztZQUM1QixNQUFNQyxVQUFVSCxZQUFZSSxNQUFNLENBQUMsQ0FBQ0MsS0FBS1YsUUFBVVUsTUFBTVYsT0FBTyxLQUFLO1lBQ3JFLE9BQU9XLEtBQUtDLEtBQUssQ0FBQ0osVUFBVSxLQUFLLEdBQUcsdUJBQXVCO1FBQzdEO1FBRUEsT0FBTztJQUNUO0lBRUEsTUFBTUssZ0JBQWdCLE9BQU9mLGFBQXFCQztRQUNoRCxNQUFNSSxTQUFTckMsVUFBVSxDQUFDZ0MsWUFBWTtRQUN0QyxJQUFJLENBQUNLLFFBQVE7UUFFYmhDLFlBQVk7UUFDWixJQUFJO2dCQXNCMEJhO1lBckI1QixNQUFNQSxZQUFZcEIsV0FBV2tELElBQUksQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRTdCLEVBQUUsS0FBS1k7WUFDaEQsSUFBSSxDQUFDZCxXQUFXO1lBRWhCLE1BQU1nQyxtQkFBbUJkLDBCQUEwQkM7WUFFbkQsMkJBQTJCO1lBQzNCLE1BQU1jLGFBQWE7Z0JBQ2pCbkI7Z0JBQ0FWLG9CQUFvQmUsT0FBT2hCLFNBQVM7Z0JBQ3BDRyxrQkFBa0JhLE9BQU9kLE9BQU87Z0JBQ2hDNkIsbUJBQW1CZixPQUFPWixZQUFZO2dCQUN0QzRCLG1CQUFtQmhCLE9BQU9YLFlBQVk7Z0JBQ3RDNEIsa0JBQWtCakIsT0FBT1osWUFBWSxJQUFJWSxPQUFPWCxZQUFZLEdBQ3hELENBQUNXLE9BQU9aLFlBQVksR0FBR1ksT0FBT1gsWUFBWSxJQUFJLElBQzlDO2dCQUNKRSxtQkFBbUJTLE9BQU9WLFFBQVE7Z0JBQ2xDdUI7Z0JBQ0FLLFFBQVE7WUFDVjtZQUVBLE1BQU1DLE1BQU10QyxVQUFVdUMsU0FBUyxHQUMzQix3QkFBNkMsUUFBckJ2QyxvQkFBQUEsVUFBVUMsTUFBTSxjQUFoQkQsd0NBQUFBLGtCQUFrQkUsRUFBRSxJQUM1QztZQUVKLE1BQU1zQyxTQUFTeEMsVUFBVXVDLFNBQVMsR0FBRyxRQUFRO1lBRTdDLE1BQU05QyxXQUFXLE1BQU1DLE1BQU00QyxLQUFLO2dCQUNoQ0U7Z0JBQ0FDLFNBQVM7b0JBQ1AsZ0JBQWdCO2dCQUNsQjtnQkFDQUMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDWDtZQUN2QjtZQUVBLElBQUl4QyxTQUFTRSxFQUFFLEVBQUU7Z0JBQ2ZKLGtCQUFrQixHQUE0QlMsT0FBekJlLE9BQU0scUJBQTZDLE9BQTFCZixVQUFVNkMsZUFBZTtnQkFDdkVDLFdBQVcsSUFBTXZELGtCQUFrQixLQUFLO2dCQUN4QyxNQUFNQyxtQkFBbUIsZUFBZTtZQUMxQyxPQUFPO2dCQUNMLE1BQU1tQixZQUFZLE1BQU1sQixTQUFTSSxJQUFJO2dCQUNyQ1IsU0FBU3NCLFVBQVV2QixLQUFLLElBQUk7WUFDOUI7UUFDRixFQUFFLE9BQU9BLE9BQU87WUFDZHdCLFFBQVF4QixLQUFLLENBQUMsdUJBQXVCQTtZQUNyQ0MsU0FBUztRQUNYLFNBQVU7WUFDUkYsWUFBWTtRQUNkO0lBQ0Y7SUFFQSxNQUFNNEQsb0JBQW9CLENBQUMvQjtRQUN6QixJQUFJQSxVQUFVLE1BQU0sT0FBTztRQUMzQixJQUFJQSxTQUFTLEtBQUssT0FBTztRQUN6QixJQUFJQSxTQUFTLEtBQUssT0FBTztRQUN6QixJQUFJQSxTQUFTLEtBQUssT0FBTztRQUN6QixJQUFJQSxTQUFTLEtBQUssT0FBTztRQUN6QixPQUFPO0lBQ1Q7SUFFQSxJQUFJaEMsV0FBVztRQUNiLHFCQUNFLDhEQUFDZ0U7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7Ozs7Ozs7Ozs7O0lBR3JCO0lBRUEscUJBQ0UsOERBQUNEO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDRDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7O3NDQUNDLDhEQUFDRTs0QkFBR0QsV0FBVTtzQ0FBbUM7Ozs7OztzQ0FDakQsOERBQUNFOzRCQUFFRixXQUFVO3NDQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFJaEM3RCx1QkFDQyw4REFBQzREO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ3pFLGlHQUFXQTt3QkFBQ3lFLFdBQVU7Ozs7OztvQkFDdEI3RDs7Ozs7OztZQUlKRSxnQ0FDQyw4REFBQzBEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQzNFLGlHQUFLQTt3QkFBQzJFLFdBQVU7Ozs7OztvQkFDaEIzRDs7Ozs7OzswQkFLTCw4REFBQzBEO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ0c7d0JBQU1ILFdBQVU7OzBDQUNmLDhEQUFDSTtnQ0FBTUosV0FBVTswQ0FDZiw0RUFBQ0s7O3NEQUNDLDhEQUFDQzs0Q0FBR04sV0FBVTtzREFBaUY7Ozs7OztzREFHL0YsOERBQUNNOzRDQUFHTixXQUFVO3NEQUFtRjs7Ozs7O3NEQUdqRyw4REFBQ007NENBQUdOLFdBQVU7c0RBQW1GOzs7Ozs7c0RBR2pHLDhEQUFDTTs0Q0FBR04sV0FBVTtzREFBbUY7Ozs7OztzREFHakcsOERBQUNNOzRDQUFHTixXQUFVO3NEQUFtRjs7Ozs7O3NEQUdqRyw4REFBQ007NENBQUdOLFdBQVU7c0RBQW1GOzs7Ozs7c0RBR2pHLDhEQUFDTTs0Q0FBR04sV0FBVTtzREFBbUY7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUtyRyw4REFBQ087Z0NBQU1QLFdBQVU7MENBQ2RyRSxXQUFXNkUsR0FBRyxDQUFDLENBQUN6RDtvQ0FDZixNQUFNbUIsU0FBU3JDLFVBQVUsQ0FBQ2tCLFVBQVVFLEVBQUUsQ0FBQyxJQUFJLENBQUM7b0NBQzVDLE1BQU13RCxlQUFleEMsMEJBQTBCQztvQ0FFL0MscUJBQ0UsOERBQUNtQzt3Q0FBc0JMLFdBQVU7OzBEQUMvQiw4REFBQ1U7Z0RBQUdWLFdBQVU7MERBQ1osNEVBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0Q7NERBQUlDLFdBQVU7c0VBQ2IsNEVBQUNEO2dFQUFJQyxXQUFVOzBFQUNiLDRFQUFDVztvRUFBS1gsV0FBVTs4RUFDYmpELFVBQVU2QyxlQUFlOzs7Ozs7Ozs7Ozs7Ozs7O3NFQUloQyw4REFBQ0c7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDRDtvRUFBSUMsV0FBVTs4RUFDWmpELFVBQVU2RCxRQUFROzs7Ozs7OEVBRXJCLDhEQUFDYjtvRUFBSUMsV0FBVTs7d0VBQXdCO3dFQUN6QixJQUFJYSxLQUFLOUQsVUFBVStELFFBQVEsRUFBRUMsa0JBQWtCOzs7Ozs7O2dFQUU1RE4sOEJBQ0MsOERBQUNWO29FQUFJQyxXQUFVOzt3RUFBd0I7d0VBQzNCUzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQVFwQiw4REFBQ0M7Z0RBQUdWLFdBQVU7MERBQ1osNEVBQUNnQjtvREFDQ0MsT0FBTy9DLE9BQU9oQixTQUFTO29EQUN2QmdFLFVBQVUsQ0FBQ25ELFFBQVVILGdCQUFnQmIsVUFBVUUsRUFBRSxFQUFFLGFBQWFjO29EQUNoRW9ELFFBQVEsSUFBTXZDLGNBQWM3QixVQUFVRSxFQUFFLEVBQUU7b0RBQzFDbUUsVUFBVW5GOzs7Ozs7Ozs7OzswREFLZCw4REFBQ3lFO2dEQUFHVixXQUFVOzBEQUNaLDRFQUFDZ0I7b0RBQ0NDLE9BQU8vQyxPQUFPZCxPQUFPO29EQUNyQjhELFVBQVUsQ0FBQ25ELFFBQVVILGdCQUFnQmIsVUFBVUUsRUFBRSxFQUFFLFdBQVdjO29EQUM5RG9ELFFBQVEsSUFBTXZDLGNBQWM3QixVQUFVRSxFQUFFLEVBQUU7b0RBQzFDbUUsVUFBVW5GOzs7Ozs7Ozs7OzswREFLZCw4REFBQ3lFO2dEQUFHVixXQUFVOzBEQUNaLDRFQUFDZ0I7b0RBQ0NDLE9BQU8vQyxPQUFPWixZQUFZO29EQUMxQjRELFVBQVUsQ0FBQ25ELFFBQVVILGdCQUFnQmIsVUFBVUUsRUFBRSxFQUFFLGdCQUFnQmM7b0RBQ25Fb0QsUUFBUSxJQUFNdkMsY0FBYzdCLFVBQVVFLEVBQUUsRUFBRTtvREFDMUNtRSxVQUFVbkY7Ozs7Ozs7Ozs7OzBEQUtkLDhEQUFDeUU7Z0RBQUdWLFdBQVU7MERBQ1osNEVBQUNnQjtvREFDQ0MsT0FBTy9DLE9BQU9YLFlBQVk7b0RBQzFCMkQsVUFBVSxDQUFDbkQsUUFBVUgsZ0JBQWdCYixVQUFVRSxFQUFFLEVBQUUsZ0JBQWdCYztvREFDbkVvRCxRQUFRLElBQU12QyxjQUFjN0IsVUFBVUUsRUFBRSxFQUFFO29EQUMxQ21FLFVBQVVuRjs7Ozs7Ozs7Ozs7MERBS2QsOERBQUN5RTtnREFBR1YsV0FBVTswREFDWiw0RUFBQ2dCO29EQUNDQyxPQUFPL0MsT0FBT1YsUUFBUTtvREFDdEIwRCxVQUFVLENBQUNuRCxRQUFVSCxnQkFBZ0JiLFVBQVVFLEVBQUUsRUFBRSxZQUFZYztvREFDL0RvRCxRQUFRLElBQU12QyxjQUFjN0IsVUFBVUUsRUFBRSxFQUFFO29EQUMxQ21FLFVBQVVuRjs7Ozs7Ozs7Ozs7MERBS2QsOERBQUN5RTtnREFBR1YsV0FBVTswREFDWiw0RUFBQ3FCO29EQUNDQyxTQUFTOzREQUF3Q3ZFOytEQUFsQ3JCLE9BQU82RixJQUFJLENBQUMsc0JBQTJELE9BQXJDeEUsRUFBQUEsb0JBQUFBLFVBQVVDLE1BQU0sY0FBaEJELHdDQUFBQSxrQkFBa0JFLEVBQUUsS0FBSUYsVUFBVUUsRUFBRTs7b0RBQ3JGK0MsV0FBVTs4REFFViw0RUFBQzFFLGlHQUFHQTt3REFBQzBFLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O3VDQWxGWmpELFVBQVVFLEVBQUU7Ozs7O2dDQXVGekI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPZDtHQWpUd0J4Qjs7UUFDUEwsc0RBQVNBOzs7S0FERks7QUEwVHhCLFNBQVN1RixrQkFBa0IsS0FBNkQ7UUFBN0QsRUFBRUMsS0FBSyxFQUFFQyxRQUFRLEVBQUVDLE1BQU0sRUFBRUMsUUFBUSxFQUEwQixHQUE3RDs7SUFDekIsTUFBTSxDQUFDSSxRQUFRQyxVQUFVLEdBQUd2RywrQ0FBUUEsQ0FBQztJQUVyQyxNQUFNNEUsb0JBQW9CLENBQUMvQjtRQUN6QixJQUFJQSxVQUFVLE1BQU0sT0FBTztRQUMzQixJQUFJQSxTQUFTLEtBQUssT0FBTztRQUN6QixJQUFJQSxTQUFTLEtBQUssT0FBTztRQUN6QixJQUFJQSxTQUFTLEtBQUssT0FBTztRQUN6QixJQUFJQSxTQUFTLEtBQUssT0FBTztRQUN6QixPQUFPO0lBQ1Q7SUFFQSxxQkFDRSw4REFBQ2dDO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDcUI7Z0JBQ0NDLFNBQVMsSUFBTUcsVUFBVSxDQUFDRDtnQkFDMUJ4QixXQUFXLDRFQUFxRyxPQUF6QkYsa0JBQWtCbUIsUUFBTztnQkFDaEhHLFVBQVVBOzBCQUVUSCxVQUFVLE9BQU8sUUFBYyxPQUFOQSxTQUFVOzs7Ozs7WUFHckNPLHdCQUNDLDhEQUFDekI7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ3FCOzRCQUNDQyxTQUFTO2dDQUNQSixTQUFTO2dDQUNUTyxVQUFVOzRCQUNaOzRCQUNBekIsV0FBVTtzQ0FDWDs7Ozs7O3dCQUdBeEUsWUFBWWdGLEdBQUcsQ0FBQyxDQUFDekMsc0JBQ2hCLDhEQUFDc0Q7Z0NBRUNDLFNBQVM7b0NBQ1BKLFNBQVNuRDtvQ0FDVDBELFVBQVU7b0NBQ1Y1QixXQUFXc0IsUUFBUSxNQUFNLDRCQUE0QjtnQ0FDdkQ7Z0NBQ0FuQixXQUFVOztvQ0FDWDtvQ0FDT2pDOzsrQkFSREE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFnQnJCO0lBcERTaUQ7TUFBQUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV2luZG93cyAxMVxcRGVza3RvcFxcY29kZXNcXElFTFRTLUNlcnRpZmljYXRpb24tU3lzdGVtXFxzcmNcXGFwcFxcZGFzaGJvYXJkXFxyZXN1bHRzXFxlbnRyeVxccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcbmltcG9ydCB7IENoZWNrLCBFeWUsIFNhdmUsIEFsZXJ0Q2lyY2xlIH0gZnJvbSAnbHVjaWRlLXJlYWN0JztcbmltcG9ydCB7IHVzZVNlc3Npb24gfSBmcm9tICduZXh0LWF1dGgvcmVhY3QnO1xuXG5pbnRlcmZhY2UgQ2FuZGlkYXRlIHtcbiAgaWQ6IHN0cmluZztcbiAgY2FuZGlkYXRlTnVtYmVyOiBzdHJpbmc7XG4gIGZ1bGxOYW1lOiBzdHJpbmc7XG4gIGVtYWlsOiBzdHJpbmc7XG4gIHRlc3REYXRlOiBzdHJpbmc7XG4gIGhhc1Jlc3VsdDogYm9vbGVhbjtcbiAgcmVzdWx0Pzoge1xuICAgIGlkOiBzdHJpbmc7XG4gICAgbGlzdGVuaW5nQmFuZFNjb3JlOiBudW1iZXIgfCBudWxsO1xuICAgIHJlYWRpbmdCYW5kU2NvcmU6IG51bWJlciB8IG51bGw7XG4gICAgd3JpdGluZ0JhbmRTY29yZTogbnVtYmVyIHwgbnVsbDtcbiAgICBzcGVha2luZ0JhbmRTY29yZTogbnVtYmVyIHwgbnVsbDtcbiAgICBvdmVyYWxsQmFuZFNjb3JlOiBudW1iZXIgfCBudWxsO1xuICAgIHN0YXR1czogJ3BlbmRpbmcnIHwgJ2NvbXBsZXRlZCcgfCAndmVyaWZpZWQnO1xuICB9O1xufVxuXG5pbnRlcmZhY2UgQmFuZFNjb3JlcyB7XG4gIFtjYW5kaWRhdGVJZDogc3RyaW5nXToge1xuICAgIGxpc3RlbmluZzogbnVtYmVyIHwgbnVsbDtcbiAgICByZWFkaW5nOiBudW1iZXIgfCBudWxsO1xuICAgIHdyaXRpbmdUYXNrMTogbnVtYmVyIHwgbnVsbDtcbiAgICB3cml0aW5nVGFzazI6IG51bWJlciB8IG51bGw7XG4gICAgc3BlYWtpbmc6IG51bWJlciB8IG51bGw7XG4gIH07XG59XG5cbmNvbnN0IEJBTkRfU0NPUkVTID0gWzEuMCwgMS41LCAyLjAsIDIuNSwgMy4wLCAzLjUsIDQuMCwgNC41LCA1LjAsIDUuNSwgNi4wLCA2LjUsIDcuMCwgNy41LCA4LjAsIDguNSwgOS4wXTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUmVzdWx0c0VudHJ5UGFnZSgpIHtcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XG4gIGNvbnN0IFtjYW5kaWRhdGVzLCBzZXRDYW5kaWRhdGVzXSA9IHVzZVN0YXRlPENhbmRpZGF0ZVtdPihbXSk7XG4gIGNvbnN0IFtiYW5kU2NvcmVzLCBzZXRCYW5kU2NvcmVzXSA9IHVzZVN0YXRlPEJhbmRTY29yZXM+KHt9KTtcbiAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xuICBjb25zdCBbaXNTYXZpbmcsIHNldElzU2F2aW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFtzdWNjZXNzTWVzc2FnZSwgc2V0U3VjY2Vzc01lc3NhZ2VdID0gdXNlU3RhdGUoJycpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgZmV0Y2hDYW5kaWRhdGVzKCk7XG4gIH0sIFtdKTtcblxuICBjb25zdCBmZXRjaENhbmRpZGF0ZXMgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvY2hlY2tlci9jYW5kaWRhdGVzP2luY2x1ZGVSZXN1bHRzPXRydWUnKTtcbiAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICBzZXRDYW5kaWRhdGVzKGRhdGEuY2FuZGlkYXRlcyB8fCBbXSk7XG5cbiAgICAgICAgLy8gSW5pdGlhbGl6ZSBiYW5kIHNjb3JlcyBmcm9tIGV4aXN0aW5nIHJlc3VsdHNcbiAgICAgICAgY29uc3QgaW5pdGlhbFNjb3JlczogQmFuZFNjb3JlcyA9IHt9O1xuICAgICAgICBkYXRhLmNhbmRpZGF0ZXM/LmZvckVhY2goKGNhbmRpZGF0ZTogQ2FuZGlkYXRlKSA9PiB7XG4gICAgICAgICAgaWYgKGNhbmRpZGF0ZS5yZXN1bHQpIHtcbiAgICAgICAgICAgIGluaXRpYWxTY29yZXNbY2FuZGlkYXRlLmlkXSA9IHtcbiAgICAgICAgICAgICAgbGlzdGVuaW5nOiBjYW5kaWRhdGUucmVzdWx0Lmxpc3RlbmluZ0JhbmRTY29yZSxcbiAgICAgICAgICAgICAgcmVhZGluZzogY2FuZGlkYXRlLnJlc3VsdC5yZWFkaW5nQmFuZFNjb3JlLFxuICAgICAgICAgICAgICB3cml0aW5nVGFzazE6IG51bGwsIC8vIFdlJ2xsIG5lZWQgdG8gY2FsY3VsYXRlIGZyb20gd3JpdGluZyBiYW5kIHNjb3JlXG4gICAgICAgICAgICAgIHdyaXRpbmdUYXNrMjogbnVsbCxcbiAgICAgICAgICAgICAgc3BlYWtpbmc6IGNhbmRpZGF0ZS5yZXN1bHQuc3BlYWtpbmdCYW5kU2NvcmUsXG4gICAgICAgICAgICB9O1xuICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgICAgIHNldEJhbmRTY29yZXMoaW5pdGlhbFNjb3Jlcyk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zdCBlcnJvckRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgIHNldEVycm9yKGVycm9yRGF0YS5lcnJvciB8fCAnRmFpbGVkIHRvIGZldGNoIGNhbmRpZGF0ZXMnKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgY2FuZGlkYXRlczonLCBlcnJvcik7XG4gICAgICBzZXRFcnJvcignQW4gZXJyb3Igb2NjdXJyZWQgd2hpbGUgZmV0Y2hpbmcgY2FuZGlkYXRlcycpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCB1cGRhdGVCYW5kU2NvcmUgPSAoY2FuZGlkYXRlSWQ6IHN0cmluZywgc2tpbGw6IGtleW9mIEJhbmRTY29yZXNbc3RyaW5nXSwgc2NvcmU6IG51bWJlciB8IG51bGwpID0+IHtcbiAgICBzZXRCYW5kU2NvcmVzKHByZXYgPT4gKHtcbiAgICAgIC4uLnByZXYsXG4gICAgICBbY2FuZGlkYXRlSWRdOiB7XG4gICAgICAgIGxpc3RlbmluZzogbnVsbCxcbiAgICAgICAgcmVhZGluZzogbnVsbCxcbiAgICAgICAgd3JpdGluZ1Rhc2sxOiBudWxsLFxuICAgICAgICB3cml0aW5nVGFzazI6IG51bGwsXG4gICAgICAgIHNwZWFraW5nOiBudWxsLFxuICAgICAgICAuLi5wcmV2W2NhbmRpZGF0ZUlkXSxcbiAgICAgICAgW3NraWxsXTogc2NvcmUsXG4gICAgICB9LFxuICAgIH0pKTtcbiAgfTtcblxuICBjb25zdCBjYWxjdWxhdGVPdmVyYWxsQmFuZFNjb3JlID0gKHNjb3JlczogQmFuZFNjb3Jlc1tzdHJpbmddKSA9PiB7XG4gICAgY29uc3QgeyBsaXN0ZW5pbmcsIHJlYWRpbmcsIHdyaXRpbmdUYXNrMSwgd3JpdGluZ1Rhc2syLCBzcGVha2luZyB9ID0gc2NvcmVzO1xuXG4gICAgLy8gQ2FsY3VsYXRlIHdyaXRpbmcgYmFuZCBzY29yZSBmcm9tIHRhc2tzXG4gICAgbGV0IHdyaXRpbmdCYW5kID0gbnVsbDtcbiAgICBpZiAod3JpdGluZ1Rhc2sxICE9PSBudWxsICYmIHdyaXRpbmdUYXNrMiAhPT0gbnVsbCkge1xuICAgICAgd3JpdGluZ0JhbmQgPSAod3JpdGluZ1Rhc2sxICsgd3JpdGluZ1Rhc2syKSAvIDI7XG4gICAgfVxuXG4gICAgY29uc3QgdmFsaWRTY29yZXMgPSBbbGlzdGVuaW5nLCByZWFkaW5nLCB3cml0aW5nQmFuZCwgc3BlYWtpbmddLmZpbHRlcihzY29yZSA9PiBzY29yZSAhPT0gbnVsbCkgYXMgbnVtYmVyW107XG5cbiAgICBpZiAodmFsaWRTY29yZXMubGVuZ3RoID09PSA0KSB7XG4gICAgICBjb25zdCBhdmVyYWdlID0gdmFsaWRTY29yZXMucmVkdWNlKChzdW0sIHNjb3JlKSA9PiBzdW0gKyBzY29yZSwgMCkgLyA0O1xuICAgICAgcmV0dXJuIE1hdGgucm91bmQoYXZlcmFnZSAqIDIpIC8gMjsgLy8gUm91bmQgdG8gbmVhcmVzdCAwLjVcbiAgICB9XG5cbiAgICByZXR1cm4gbnVsbDtcbiAgfTtcblxuICBjb25zdCBzYXZlQmFuZFNjb3JlID0gYXN5bmMgKGNhbmRpZGF0ZUlkOiBzdHJpbmcsIHNraWxsOiBrZXlvZiBCYW5kU2NvcmVzW3N0cmluZ10pID0+IHtcbiAgICBjb25zdCBzY29yZXMgPSBiYW5kU2NvcmVzW2NhbmRpZGF0ZUlkXTtcbiAgICBpZiAoIXNjb3JlcykgcmV0dXJuO1xuXG4gICAgc2V0SXNTYXZpbmcodHJ1ZSk7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGNhbmRpZGF0ZSA9IGNhbmRpZGF0ZXMuZmluZChjID0+IGMuaWQgPT09IGNhbmRpZGF0ZUlkKTtcbiAgICAgIGlmICghY2FuZGlkYXRlKSByZXR1cm47XG5cbiAgICAgIGNvbnN0IG92ZXJhbGxCYW5kU2NvcmUgPSBjYWxjdWxhdGVPdmVyYWxsQmFuZFNjb3JlKHNjb3Jlcyk7XG5cbiAgICAgIC8vIFByZXBhcmUgdGhlIGRhdGEgZm9yIEFQSVxuICAgICAgY29uc3QgcmVzdWx0RGF0YSA9IHtcbiAgICAgICAgY2FuZGlkYXRlSWQsXG4gICAgICAgIGxpc3RlbmluZ0JhbmRTY29yZTogc2NvcmVzLmxpc3RlbmluZyxcbiAgICAgICAgcmVhZGluZ0JhbmRTY29yZTogc2NvcmVzLnJlYWRpbmcsXG4gICAgICAgIHdyaXRpbmdUYXNrMVNjb3JlOiBzY29yZXMud3JpdGluZ1Rhc2sxLFxuICAgICAgICB3cml0aW5nVGFzazJTY29yZTogc2NvcmVzLndyaXRpbmdUYXNrMixcbiAgICAgICAgd3JpdGluZ0JhbmRTY29yZTogc2NvcmVzLndyaXRpbmdUYXNrMSAmJiBzY29yZXMud3JpdGluZ1Rhc2syXG4gICAgICAgICAgPyAoc2NvcmVzLndyaXRpbmdUYXNrMSArIHNjb3Jlcy53cml0aW5nVGFzazIpIC8gMlxuICAgICAgICAgIDogbnVsbCxcbiAgICAgICAgc3BlYWtpbmdCYW5kU2NvcmU6IHNjb3Jlcy5zcGVha2luZyxcbiAgICAgICAgb3ZlcmFsbEJhbmRTY29yZSxcbiAgICAgICAgc3RhdHVzOiAnY29tcGxldGVkJyBhcyBjb25zdCxcbiAgICAgIH07XG5cbiAgICAgIGNvbnN0IHVybCA9IGNhbmRpZGF0ZS5oYXNSZXN1bHRcbiAgICAgICAgPyBgL2FwaS9jaGVja2VyL3Jlc3VsdHMvJHtjYW5kaWRhdGUucmVzdWx0Py5pZH1gXG4gICAgICAgIDogJy9hcGkvYWRtaW4vcmVzdWx0cyc7XG5cbiAgICAgIGNvbnN0IG1ldGhvZCA9IGNhbmRpZGF0ZS5oYXNSZXN1bHQgPyAnUFVUJyA6ICdQT1NUJztcblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCh1cmwsIHtcbiAgICAgICAgbWV0aG9kLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkocmVzdWx0RGF0YSksXG4gICAgICB9KTtcblxuICAgICAgaWYgKHJlc3BvbnNlLm9rKSB7XG4gICAgICAgIHNldFN1Y2Nlc3NNZXNzYWdlKGAke3NraWxsfSBzY29yZSBzYXZlZCBmb3IgJHtjYW5kaWRhdGUuY2FuZGlkYXRlTnVtYmVyfWApO1xuICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHNldFN1Y2Nlc3NNZXNzYWdlKCcnKSwgMzAwMCk7XG4gICAgICAgIGF3YWl0IGZldGNoQ2FuZGlkYXRlcygpOyAvLyBSZWZyZXNoIGRhdGFcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnN0IGVycm9yRGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgc2V0RXJyb3IoZXJyb3JEYXRhLmVycm9yIHx8ICdGYWlsZWQgdG8gc2F2ZSBzY29yZScpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBzYXZpbmcgc2NvcmU6JywgZXJyb3IpO1xuICAgICAgc2V0RXJyb3IoJ0FuIGVycm9yIG9jY3VycmVkIHdoaWxlIHNhdmluZyB0aGUgc2NvcmUnKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNTYXZpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBnZXRCYW5kU2NvcmVDb2xvciA9IChzY29yZTogbnVtYmVyIHwgbnVsbCkgPT4ge1xuICAgIGlmIChzY29yZSA9PT0gbnVsbCkgcmV0dXJuICdiZy1ncmF5LTEwMCB0ZXh0LWdyYXktNDAwJztcbiAgICBpZiAoc2NvcmUgPj0gOC41KSByZXR1cm4gJ2JnLWdyZWVuLTEwMCB0ZXh0LWdyZWVuLTgwMCc7XG4gICAgaWYgKHNjb3JlID49IDcuMCkgcmV0dXJuICdiZy1ibHVlLTEwMCB0ZXh0LWJsdWUtODAwJztcbiAgICBpZiAoc2NvcmUgPj0gNi4wKSByZXR1cm4gJ2JnLXllbGxvdy0xMDAgdGV4dC15ZWxsb3ctODAwJztcbiAgICBpZiAoc2NvcmUgPj0gNS4wKSByZXR1cm4gJ2JnLW9yYW5nZS0xMDAgdGV4dC1vcmFuZ2UtODAwJztcbiAgICByZXR1cm4gJ2JnLXJlZC0xMDAgdGV4dC1yZWQtODAwJztcbiAgfTtcblxuICBpZiAoaXNMb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgaC02NFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC04IHctOCBib3JkZXItYi0yIGJvcmRlci1ibHVlLTYwMFwiPjwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyXCI+XG4gICAgICAgIDxkaXY+XG4gICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+UmVzdWx0cyBFbnRyeTwvaDE+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPlF1aWNrbHkgYXNzaWduIGJhbmQgc2NvcmVzIHRvIGNhbmRpZGF0ZXM8L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHtlcnJvciAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctcmVkLTUwIGJvcmRlciBib3JkZXItcmVkLTIwMCB0ZXh0LXJlZC02MDAgcHgtNCBweS0zIHJvdW5kZWQtbWQgdGV4dC1zbSBmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgIDxBbGVydENpcmNsZSBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgIHtlcnJvcn1cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7c3VjY2Vzc01lc3NhZ2UgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyZWVuLTUwIGJvcmRlciBib3JkZXItZ3JlZW4tMjAwIHRleHQtZ3JlZW4tNjAwIHB4LTQgcHktMyByb3VuZGVkLW1kIHRleHQtc20gZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICA8Q2hlY2sgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICB7c3VjY2Vzc01lc3NhZ2V9XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cblxuICAgICAgey8qIFJlc3VsdHMgRW50cnkgVGFibGUgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHNoYWRvdyByb3VuZGVkLWxnIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm92ZXJmbG93LXgtYXV0b1wiPlxuICAgICAgICAgIDx0YWJsZSBjbGFzc05hbWU9XCJtaW4tdy1mdWxsIGRpdmlkZS15IGRpdmlkZS1ncmF5LTIwMFwiPlxuICAgICAgICAgICAgPHRoZWFkIGNsYXNzTmFtZT1cImJnLWdyYXktNTBcIj5cbiAgICAgICAgICAgICAgPHRyPlxuICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1sZWZ0IHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cbiAgICAgICAgICAgICAgICAgIENhbmRpZGF0ZVxuICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTYgcHktMyB0ZXh0LWNlbnRlciB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICBMaXN0ZW5pbmdcbiAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1jZW50ZXIgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgICAgICAgUmVhZGluZ1xuICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTYgcHktMyB0ZXh0LWNlbnRlciB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICBXcml0aW5nIFRhc2sgMVxuICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTYgcHktMyB0ZXh0LWNlbnRlciB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICBXcml0aW5nIFRhc2sgMlxuICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTYgcHktMyB0ZXh0LWNlbnRlciB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICBTcGVha2luZ1xuICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTYgcHktMyB0ZXh0LWNlbnRlciB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICBBY3Rpb25zXG4gICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgPC90cj5cbiAgICAgICAgICAgIDwvdGhlYWQ+XG4gICAgICAgICAgICA8dGJvZHkgY2xhc3NOYW1lPVwiYmctd2hpdGUgZGl2aWRlLXkgZGl2aWRlLWdyYXktMjAwXCI+XG4gICAgICAgICAgICAgIHtjYW5kaWRhdGVzLm1hcCgoY2FuZGlkYXRlKSA9PiB7XG4gICAgICAgICAgICAgICAgY29uc3Qgc2NvcmVzID0gYmFuZFNjb3Jlc1tjYW5kaWRhdGUuaWRdIHx8IHt9O1xuICAgICAgICAgICAgICAgIGNvbnN0IG92ZXJhbGxTY29yZSA9IGNhbGN1bGF0ZU92ZXJhbGxCYW5kU2NvcmUoc2NvcmVzKTtcblxuICAgICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgICA8dHIga2V5PXtjYW5kaWRhdGUuaWR9IGNsYXNzTmFtZT1cImhvdmVyOmJnLWdyYXktNTBcIj5cbiAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNCB3aGl0ZXNwYWNlLW5vd3JhcFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC1zaHJpbmstMCBoLTEwIHctMTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTEwIHctMTAgcm91bmRlZC1mdWxsIGJnLWJsdWUtMTAwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWJsdWUtODAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y2FuZGlkYXRlLmNhbmRpZGF0ZU51bWJlcn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1sLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y2FuZGlkYXRlLmZ1bGxOYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBUZXN0IERhdGU6IHtuZXcgRGF0ZShjYW5kaWRhdGUudGVzdERhdGUpLnRvTG9jYWxlRGF0ZVN0cmluZygpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge292ZXJhbGxTY29yZSAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIE92ZXJhbGw6IHtvdmVyYWxsU2NvcmV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC90ZD5cblxuICAgICAgICAgICAgICAgICAgICB7LyogTGlzdGVuaW5nICovfVxuICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwIHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPEJhbmRTY29yZVNlbGVjdG9yXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17c2NvcmVzLmxpc3RlbmluZ31cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoc2NvcmUpID0+IHVwZGF0ZUJhbmRTY29yZShjYW5kaWRhdGUuaWQsICdsaXN0ZW5pbmcnLCBzY29yZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICBvblNhdmU9eygpID0+IHNhdmVCYW5kU2NvcmUoY2FuZGlkYXRlLmlkLCAnbGlzdGVuaW5nJyl9XG4gICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNTYXZpbmd9XG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPC90ZD5cblxuICAgICAgICAgICAgICAgICAgICB7LyogUmVhZGluZyAqL31cbiAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNCB3aGl0ZXNwYWNlLW5vd3JhcCB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxCYW5kU2NvcmVTZWxlY3RvclxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3Njb3Jlcy5yZWFkaW5nfVxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhzY29yZSkgPT4gdXBkYXRlQmFuZFNjb3JlKGNhbmRpZGF0ZS5pZCwgJ3JlYWRpbmcnLCBzY29yZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICBvblNhdmU9eygpID0+IHNhdmVCYW5kU2NvcmUoY2FuZGlkYXRlLmlkLCAncmVhZGluZycpfVxuICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzU2F2aW5nfVxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvdGQ+XG5cbiAgICAgICAgICAgICAgICAgICAgey8qIFdyaXRpbmcgVGFzayAxICovfVxuICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwIHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPEJhbmRTY29yZVNlbGVjdG9yXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17c2NvcmVzLndyaXRpbmdUYXNrMX1cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoc2NvcmUpID0+IHVwZGF0ZUJhbmRTY29yZShjYW5kaWRhdGUuaWQsICd3cml0aW5nVGFzazEnLCBzY29yZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICBvblNhdmU9eygpID0+IHNhdmVCYW5kU2NvcmUoY2FuZGlkYXRlLmlkLCAnd3JpdGluZ1Rhc2sxJyl9XG4gICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNTYXZpbmd9XG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPC90ZD5cblxuICAgICAgICAgICAgICAgICAgICB7LyogV3JpdGluZyBUYXNrIDIgKi99XG4gICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXAgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8QmFuZFNjb3JlU2VsZWN0b3JcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtzY29yZXMud3JpdGluZ1Rhc2syfVxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhzY29yZSkgPT4gdXBkYXRlQmFuZFNjb3JlKGNhbmRpZGF0ZS5pZCwgJ3dyaXRpbmdUYXNrMicsIHNjb3JlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uU2F2ZT17KCkgPT4gc2F2ZUJhbmRTY29yZShjYW5kaWRhdGUuaWQsICd3cml0aW5nVGFzazInKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc1NhdmluZ31cbiAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICA8L3RkPlxuXG4gICAgICAgICAgICAgICAgICAgIHsvKiBTcGVha2luZyAqL31cbiAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNCB3aGl0ZXNwYWNlLW5vd3JhcCB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxCYW5kU2NvcmVTZWxlY3RvclxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3Njb3Jlcy5zcGVha2luZ31cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoc2NvcmUpID0+IHVwZGF0ZUJhbmRTY29yZShjYW5kaWRhdGUuaWQsICdzcGVha2luZycsIHNjb3JlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uU2F2ZT17KCkgPT4gc2F2ZUJhbmRTY29yZShjYW5kaWRhdGUuaWQsICdzcGVha2luZycpfVxuICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzU2F2aW5nfVxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvdGQ+XG5cbiAgICAgICAgICAgICAgICAgICAgey8qIEFjdGlvbnMgKi99XG4gICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXAgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByb3V0ZXIucHVzaChgL2Rhc2hib2FyZC9yZXN1bHRzLyR7Y2FuZGlkYXRlLnJlc3VsdD8uaWQgfHwgY2FuZGlkYXRlLmlkfWApfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTYwMCBob3Zlcjp0ZXh0LWJsdWUtOTAwIHRleHQtc21cIlxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxFeWUgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICA8L3RyPlxuICAgICAgICAgICAgICAgICk7XG4gICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgPC90Ym9keT5cbiAgICAgICAgICA8L3RhYmxlPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufVxuXG5pbnRlcmZhY2UgQmFuZFNjb3JlU2VsZWN0b3JQcm9wcyB7XG4gIHZhbHVlOiBudW1iZXIgfCBudWxsO1xuICBvbkNoYW5nZTogKHNjb3JlOiBudW1iZXIgfCBudWxsKSA9PiB2b2lkO1xuICBvblNhdmU6ICgpID0+IHZvaWQ7XG4gIGRpc2FibGVkOiBib29sZWFuO1xufVxuXG5mdW5jdGlvbiBCYW5kU2NvcmVTZWxlY3Rvcih7IHZhbHVlLCBvbkNoYW5nZSwgb25TYXZlLCBkaXNhYmxlZCB9OiBCYW5kU2NvcmVTZWxlY3RvclByb3BzKSB7XG4gIGNvbnN0IFtpc09wZW4sIHNldElzT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgY29uc3QgZ2V0QmFuZFNjb3JlQ29sb3IgPSAoc2NvcmU6IG51bWJlciB8IG51bGwpID0+IHtcbiAgICBpZiAoc2NvcmUgPT09IG51bGwpIHJldHVybiAnYmctZ3JheS0xMDAgdGV4dC1ncmF5LTQwMCBib3JkZXItZ3JheS0yMDAnO1xuICAgIGlmIChzY29yZSA+PSA4LjUpIHJldHVybiAnYmctZ3JlZW4tMTAwIHRleHQtZ3JlZW4tODAwIGJvcmRlci1ncmVlbi0yMDAnO1xuICAgIGlmIChzY29yZSA+PSA3LjApIHJldHVybiAnYmctYmx1ZS0xMDAgdGV4dC1ibHVlLTgwMCBib3JkZXItYmx1ZS0yMDAnO1xuICAgIGlmIChzY29yZSA+PSA2LjApIHJldHVybiAnYmcteWVsbG93LTEwMCB0ZXh0LXllbGxvdy04MDAgYm9yZGVyLXllbGxvdy0yMDAnO1xuICAgIGlmIChzY29yZSA+PSA1LjApIHJldHVybiAnYmctb3JhbmdlLTEwMCB0ZXh0LW9yYW5nZS04MDAgYm9yZGVyLW9yYW5nZS0yMDAnO1xuICAgIHJldHVybiAnYmctcmVkLTEwMCB0ZXh0LXJlZC04MDAgYm9yZGVyLXJlZC0yMDAnO1xuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgPGJ1dHRvblxuICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc09wZW4oIWlzT3Blbil9XG4gICAgICAgIGNsYXNzTmFtZT17YGlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC0zIHB5LTIgYm9yZGVyIHJvdW5kZWQtbWQgdGV4dC1zbSBmb250LW1lZGl1bSAke2dldEJhbmRTY29yZUNvbG9yKHZhbHVlKX0gaG92ZXI6b3BhY2l0eS04MCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDBgfVxuICAgICAgICBkaXNhYmxlZD17ZGlzYWJsZWR9XG4gICAgICA+XG4gICAgICAgIHt2YWx1ZSAhPT0gbnVsbCA/IGBCYW5kICR7dmFsdWV9YCA6ICdQZW5kaW5nJ31cbiAgICAgIDwvYnV0dG9uPlxuXG4gICAgICB7aXNPcGVuICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB6LTEwIG10LTEgdy0zMiBiZy13aGl0ZSBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHJvdW5kZWQtbWQgc2hhZG93LWxnXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJweS0xIG1heC1oLTQ4IG92ZXJmbG93LXktYXV0b1wiPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgb25DaGFuZ2UobnVsbCk7XG4gICAgICAgICAgICAgICAgc2V0SXNPcGVuKGZhbHNlKTtcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmxvY2sgdy1mdWxsIHB4LTMgcHktMiB0ZXh0LWxlZnQgdGV4dC1zbSB0ZXh0LWdyYXktNzAwIGhvdmVyOmJnLWdyYXktMTAwXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgUGVuZGluZ1xuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICB7QkFORF9TQ09SRVMubWFwKChzY29yZSkgPT4gKFxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAga2V5PXtzY29yZX1cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZShzY29yZSk7XG4gICAgICAgICAgICAgICAgICBzZXRJc09wZW4oZmFsc2UpO1xuICAgICAgICAgICAgICAgICAgc2V0VGltZW91dChvblNhdmUsIDEwMCk7IC8vIEF1dG8tc2F2ZSBhZnRlciBzZWxlY3Rpb25cbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJsb2NrIHctZnVsbCBweC0zIHB5LTIgdGV4dC1sZWZ0IHRleHQtc20gdGV4dC1ncmF5LTcwMCBob3ZlcjpiZy1ncmF5LTEwMFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBCYW5kIHtzY29yZX1cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICApKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlUm91dGVyIiwiQ2hlY2siLCJFeWUiLCJBbGVydENpcmNsZSIsIkJBTkRfU0NPUkVTIiwiUmVzdWx0c0VudHJ5UGFnZSIsInJvdXRlciIsImNhbmRpZGF0ZXMiLCJzZXRDYW5kaWRhdGVzIiwiYmFuZFNjb3JlcyIsInNldEJhbmRTY29yZXMiLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJpc1NhdmluZyIsInNldElzU2F2aW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsInN1Y2Nlc3NNZXNzYWdlIiwic2V0U3VjY2Vzc01lc3NhZ2UiLCJmZXRjaENhbmRpZGF0ZXMiLCJyZXNwb25zZSIsImZldGNoIiwib2siLCJkYXRhIiwianNvbiIsImluaXRpYWxTY29yZXMiLCJmb3JFYWNoIiwiY2FuZGlkYXRlIiwicmVzdWx0IiwiaWQiLCJsaXN0ZW5pbmciLCJsaXN0ZW5pbmdCYW5kU2NvcmUiLCJyZWFkaW5nIiwicmVhZGluZ0JhbmRTY29yZSIsIndyaXRpbmdUYXNrMSIsIndyaXRpbmdUYXNrMiIsInNwZWFraW5nIiwic3BlYWtpbmdCYW5kU2NvcmUiLCJlcnJvckRhdGEiLCJjb25zb2xlIiwidXBkYXRlQmFuZFNjb3JlIiwiY2FuZGlkYXRlSWQiLCJza2lsbCIsInNjb3JlIiwicHJldiIsImNhbGN1bGF0ZU92ZXJhbGxCYW5kU2NvcmUiLCJzY29yZXMiLCJ3cml0aW5nQmFuZCIsInZhbGlkU2NvcmVzIiwiZmlsdGVyIiwibGVuZ3RoIiwiYXZlcmFnZSIsInJlZHVjZSIsInN1bSIsIk1hdGgiLCJyb3VuZCIsInNhdmVCYW5kU2NvcmUiLCJmaW5kIiwiYyIsIm92ZXJhbGxCYW5kU2NvcmUiLCJyZXN1bHREYXRhIiwid3JpdGluZ1Rhc2sxU2NvcmUiLCJ3cml0aW5nVGFzazJTY29yZSIsIndyaXRpbmdCYW5kU2NvcmUiLCJzdGF0dXMiLCJ1cmwiLCJoYXNSZXN1bHQiLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJjYW5kaWRhdGVOdW1iZXIiLCJzZXRUaW1lb3V0IiwiZ2V0QmFuZFNjb3JlQ29sb3IiLCJkaXYiLCJjbGFzc05hbWUiLCJoMSIsInAiLCJ0YWJsZSIsInRoZWFkIiwidHIiLCJ0aCIsInRib2R5IiwibWFwIiwib3ZlcmFsbFNjb3JlIiwidGQiLCJzcGFuIiwiZnVsbE5hbWUiLCJEYXRlIiwidGVzdERhdGUiLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJCYW5kU2NvcmVTZWxlY3RvciIsInZhbHVlIiwib25DaGFuZ2UiLCJvblNhdmUiLCJkaXNhYmxlZCIsImJ1dHRvbiIsIm9uQ2xpY2siLCJwdXNoIiwiaXNPcGVuIiwic2V0SXNPcGVuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/results/entry/page.tsx\n"));

/***/ })

});