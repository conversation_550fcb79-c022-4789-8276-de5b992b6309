"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/results/entry/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/results/entry/page.tsx":
/*!**************************************************!*\
  !*** ./src/app/dashboard/results/entry/page.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ResultsEntryPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Eye!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Eye!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Eye!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst BAND_SCORES = [\n    1.0,\n    1.5,\n    2.0,\n    2.5,\n    3.0,\n    3.5,\n    4.0,\n    4.5,\n    5.0,\n    5.5,\n    6.0,\n    6.5,\n    7.0,\n    7.5,\n    8.0,\n    8.5,\n    9.0\n];\nfunction ResultsEntryPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [candidates, setCandidates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [bandScores, setBandScores] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [successMessage, setSuccessMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ResultsEntryPage.useEffect\": ()=>{\n            fetchCandidates();\n        }\n    }[\"ResultsEntryPage.useEffect\"], []);\n    const fetchCandidates = async ()=>{\n        try {\n            const response = await fetch('/api/checker/candidates?includeResults=true');\n            if (response.ok) {\n                var _data_candidates;\n                const data = await response.json();\n                setCandidates(data.candidates || []);\n                // Initialize band scores from existing results\n                const initialScores = {};\n                (_data_candidates = data.candidates) === null || _data_candidates === void 0 ? void 0 : _data_candidates.forEach((candidate)=>{\n                    if (candidate.result) {\n                        initialScores[candidate.id] = {\n                            listening: candidate.result.listeningBandScore,\n                            reading: candidate.result.readingBandScore,\n                            writingTask1: null,\n                            writingTask2: null,\n                            speaking: candidate.result.speakingBandScore\n                        };\n                    }\n                });\n                setBandScores(initialScores);\n            } else {\n                const errorData = await response.json();\n                setError(errorData.error || 'Failed to fetch candidates');\n            }\n        } catch (error) {\n            console.error('Error fetching candidates:', error);\n            setError('An error occurred while fetching candidates');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const updateBandScore = (candidateId, skill, score)=>{\n        setBandScores((prev)=>({\n                ...prev,\n                [candidateId]: {\n                    listening: null,\n                    reading: null,\n                    writingTask1: null,\n                    writingTask2: null,\n                    speaking: null,\n                    ...prev[candidateId],\n                    [skill]: score\n                }\n            }));\n    };\n    const calculateOverallBandScore = (scores)=>{\n        const { listening, reading, writingTask1, writingTask2, speaking } = scores;\n        // Calculate writing band score from tasks\n        let writingBand = null;\n        if (writingTask1 !== null && writingTask2 !== null) {\n            writingBand = (writingTask1 + writingTask2) / 2;\n        }\n        const validScores = [\n            listening,\n            reading,\n            writingBand,\n            speaking\n        ].filter((score)=>score !== null);\n        if (validScores.length === 4) {\n            const average = validScores.reduce((sum, score)=>sum + score, 0) / 4;\n            return Math.round(average * 2) / 2; // Round to nearest 0.5\n        }\n        return null;\n    };\n    const saveBandScore = async (candidateId, skill)=>{\n        const scores = bandScores[candidateId];\n        if (!scores) return;\n        setIsSaving(true);\n        try {\n            var _candidate_result;\n            const candidate = candidates.find((c)=>c.id === candidateId);\n            if (!candidate) return;\n            const overallBandScore = calculateOverallBandScore(scores);\n            // Prepare the data for API\n            const resultData = {\n                candidateId,\n                listeningBandScore: scores.listening,\n                readingBandScore: scores.reading,\n                writingTask1Score: scores.writingTask1,\n                writingTask2Score: scores.writingTask2,\n                writingBandScore: scores.writingTask1 && scores.writingTask2 ? (scores.writingTask1 + scores.writingTask2) / 2 : null,\n                speakingBandScore: scores.speaking,\n                overallBandScore,\n                status: 'completed'\n            };\n            const url = candidate.hasResult ? \"/api/checker/results/\".concat((_candidate_result = candidate.result) === null || _candidate_result === void 0 ? void 0 : _candidate_result.id) : '/api/admin/results';\n            const method = candidate.hasResult ? 'PUT' : 'POST';\n            const response = await fetch(url, {\n                method,\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(resultData)\n            });\n            if (response.ok) {\n                setSuccessMessage(\"\".concat(skill, \" score saved for \").concat(candidate.candidateNumber));\n                setTimeout(()=>setSuccessMessage(''), 3000);\n                await fetchCandidates(); // Refresh data\n            } else {\n                const errorData = await response.json();\n                setError(errorData.error || 'Failed to save score');\n            }\n        } catch (error) {\n            console.error('Error saving score:', error);\n            setError('An error occurred while saving the score');\n        } finally{\n            setIsSaving(false);\n        }\n    };\n    const getBandScoreColor = (score)=>{\n        if (score === null) return 'bg-gray-100 text-gray-400';\n        if (score >= 8.5) return 'bg-green-100 text-green-800';\n        if (score >= 7.0) return 'bg-blue-100 text-blue-800';\n        if (score >= 6.0) return 'bg-yellow-100 text-yellow-800';\n        if (score >= 5.0) return 'bg-orange-100 text-orange-800';\n        return 'bg-red-100 text-red-800';\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                lineNumber: 185,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n            lineNumber: 184,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: \"Results Entry\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Quickly assign band scores to candidates\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                    lineNumber: 193,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"h-4 w-4 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 11\n                    }, this),\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                lineNumber: 200,\n                columnNumber: 9\n            }, this),\n            successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-md text-sm flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"h-4 w-4 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this),\n                    successMessage\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                lineNumber: 207,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow rounded-lg overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"overflow-x-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: \"min-w-full divide-y divide-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                className: \"bg-gray-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Candidate\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Listening\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Reading\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Writing Task 1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Writing Task 2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Speaking\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                className: \"bg-white divide-y divide-gray-200\",\n                                children: candidates.map((candidate)=>{\n                                    const scores = bandScores[candidate.id] || {};\n                                    const overallScore = calculateOverallBandScore(scores);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        className: \"hover:bg-gray-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-shrink-0 h-10 w-10\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium text-blue-800\",\n                                                                    children: candidate.candidateNumber\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                                    lineNumber: 253,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                            lineNumber: 251,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"ml-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium text-gray-900\",\n                                                                    children: candidate.fullName\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                                    lineNumber: 259,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: [\n                                                                        \"Test Date: \",\n                                                                        new Date(candidate.testDate).toLocaleDateString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                                    lineNumber: 262,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                overallScore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-400\",\n                                                                    children: [\n                                                                        \"Overall: \",\n                                                                        overallScore\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                                    lineNumber: 266,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                            lineNumber: 258,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BandScoreSelector, {\n                                                    value: scores.listening,\n                                                    onChange: (score)=>updateBandScore(candidate.id, 'listening', score),\n                                                    onSave: ()=>saveBandScore(candidate.id, 'listening'),\n                                                    disabled: isSaving\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BandScoreSelector, {\n                                                    value: scores.reading,\n                                                    onChange: (score)=>updateBandScore(candidate.id, 'reading', score),\n                                                    onSave: ()=>saveBandScore(candidate.id, 'reading'),\n                                                    disabled: isSaving\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BandScoreSelector, {\n                                                    value: scores.writingTask1,\n                                                    onChange: (score)=>updateBandScore(candidate.id, 'writingTask1', score),\n                                                    onSave: ()=>saveBandScore(candidate.id, 'writingTask1'),\n                                                    disabled: isSaving\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BandScoreSelector, {\n                                                    value: scores.writingTask2,\n                                                    onChange: (score)=>updateBandScore(candidate.id, 'writingTask2', score),\n                                                    onSave: ()=>saveBandScore(candidate.id, 'writingTask2'),\n                                                    disabled: isSaving\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BandScoreSelector, {\n                                                    value: scores.speaking,\n                                                    onChange: (score)=>updateBandScore(candidate.id, 'speaking', score),\n                                                    onSave: ()=>saveBandScore(candidate.id, 'speaking'),\n                                                    disabled: isSaving\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        var _candidate_result;\n                                                        return router.push(\"/dashboard/results/\".concat(((_candidate_result = candidate.result) === null || _candidate_result === void 0 ? void 0 : _candidate_result.id) || candidate.id));\n                                                    },\n                                                    className: \"text-blue-600 hover:text-blue-900 text-sm\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, candidate.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n        lineNumber: 191,\n        columnNumber: 5\n    }, this);\n}\n_s(ResultsEntryPage, \"HlSUETyJrtCLVDhILNNP2yWpyes=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = ResultsEntryPage;\nfunction BandScoreSelector(param) {\n    let { value, onChange, onSave, disabled } = param;\n    _s1();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const getBandScoreColor = (score)=>{\n        if (score === null) return 'bg-gray-100 text-gray-400 border-gray-200';\n        if (score >= 8.5) return 'bg-green-100 text-green-800 border-green-200';\n        if (score >= 7.0) return 'bg-blue-100 text-blue-800 border-blue-200';\n        if (score >= 6.0) return 'bg-yellow-100 text-yellow-800 border-yellow-200';\n        if (score >= 5.0) return 'bg-orange-100 text-orange-800 border-orange-200';\n        return 'bg-red-100 text-red-800 border-red-200';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"inline-flex items-center px-3 py-2 border rounded-md text-sm font-medium \".concat(getBandScoreColor(value), \" hover:opacity-80 focus:outline-none focus:ring-2 focus:ring-blue-500\"),\n                disabled: disabled,\n                children: value !== null ? \"Band \".concat(value) : 'Pending'\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                lineNumber: 365,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute z-10 mt-1 w-32 bg-white border border-gray-200 rounded-md shadow-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"py-1 max-h-48 overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                onChange(null);\n                                setIsOpen(false);\n                            },\n                            className: \"block w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-100\",\n                            children: \"Pending\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                            lineNumber: 376,\n                            columnNumber: 13\n                        }, this),\n                        BAND_SCORES.map((score)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    onChange(score);\n                                    setIsOpen(false);\n                                    setTimeout(onSave, 100); // Auto-save after selection\n                                },\n                                className: \"block w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-100\",\n                                children: [\n                                    \"Band \",\n                                    score\n                                ]\n                            }, score, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                lineNumber: 386,\n                                columnNumber: 15\n                            }, this))\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                    lineNumber: 375,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                lineNumber: 374,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n        lineNumber: 364,\n        columnNumber: 5\n    }, this);\n}\n_s1(BandScoreSelector, \"+sus0Lb0ewKHdwiUhiTAJFoFyQ0=\");\n_c1 = BandScoreSelector;\nvar _c, _c1;\n$RefreshReg$(_c, \"ResultsEntryPage\");\n$RefreshReg$(_c1, \"BandScoreSelector\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/results/entry/page.tsx\n"));

/***/ })

});