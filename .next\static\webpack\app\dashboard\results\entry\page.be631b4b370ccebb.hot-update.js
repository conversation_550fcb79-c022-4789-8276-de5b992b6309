"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/results/entry/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/results/entry/page.tsx":
/*!**************************************************!*\
  !*** ./src/app/dashboard/results/entry/page.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ResultsEntryPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Eye!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Eye!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Eye!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\nconst BAND_SCORES = [\n    1.0,\n    1.5,\n    2.0,\n    2.5,\n    3.0,\n    3.5,\n    4.0,\n    4.5,\n    5.0,\n    5.5,\n    6.0,\n    6.5,\n    7.0,\n    7.5,\n    8.0,\n    8.5,\n    9.0\n];\nfunction ResultsEntryPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    const [candidates, setCandidates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [bandScores, setBandScores] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [successMessage, setSuccessMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ResultsEntryPage.useEffect\": ()=>{\n            if (status === 'loading') return;\n            if (!session) {\n                router.push('/auth/signin');\n                return;\n            }\n            fetchCandidates();\n        }\n    }[\"ResultsEntryPage.useEffect\"], [\n        session,\n        status,\n        router\n    ]);\n    const fetchCandidates = async ()=>{\n        try {\n            const response = await fetch('/api/checker/candidates?includeResults=true');\n            if (response.ok) {\n                var _data_candidates;\n                const data = await response.json();\n                setCandidates(data.candidates || []);\n                // Initialize band scores from existing results\n                const initialScores = {};\n                (_data_candidates = data.candidates) === null || _data_candidates === void 0 ? void 0 : _data_candidates.forEach((candidate)=>{\n                    if (candidate.result) {\n                        initialScores[candidate.id] = {\n                            listening: candidate.result.listeningBandScore,\n                            reading: candidate.result.readingBandScore,\n                            writingTask1: null,\n                            writingTask2: null,\n                            speaking: candidate.result.speakingBandScore\n                        };\n                    }\n                });\n                setBandScores(initialScores);\n            } else {\n                const errorData = await response.json();\n                setError(errorData.error || 'Failed to fetch candidates');\n            }\n        } catch (error) {\n            console.error('Error fetching candidates:', error);\n            setError('An error occurred while fetching candidates');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const updateBandScore = (candidateId, skill, score)=>{\n        setBandScores((prev)=>({\n                ...prev,\n                [candidateId]: {\n                    listening: null,\n                    reading: null,\n                    writingTask1: null,\n                    writingTask2: null,\n                    speaking: null,\n                    ...prev[candidateId],\n                    [skill]: score\n                }\n            }));\n    };\n    const calculateOverallBandScore = (scores)=>{\n        const { listening, reading, writingTask1, writingTask2, speaking } = scores;\n        // Calculate writing band score from tasks\n        let writingBand = null;\n        if (writingTask1 !== null && writingTask2 !== null) {\n            writingBand = (writingTask1 + writingTask2) / 2;\n        }\n        const validScores = [\n            listening,\n            reading,\n            writingBand,\n            speaking\n        ].filter((score)=>score !== null);\n        if (validScores.length === 4) {\n            const average = validScores.reduce((sum, score)=>sum + score, 0) / 4;\n            return Math.round(average * 2) / 2; // Round to nearest 0.5\n        }\n        return null;\n    };\n    const saveBandScore = async (candidateId, skill)=>{\n        const scores = bandScores[candidateId];\n        if (!scores) return;\n        setIsSaving(true);\n        try {\n            var _candidate_result;\n            const candidate = candidates.find((c)=>c.id === candidateId);\n            if (!candidate) return;\n            const overallBandScore = calculateOverallBandScore(scores);\n            // Prepare the data for API\n            const resultData = {\n                candidateId,\n                listeningBandScore: scores.listening,\n                readingBandScore: scores.reading,\n                writingTask1Score: scores.writingTask1,\n                writingTask2Score: scores.writingTask2,\n                writingBandScore: scores.writingTask1 && scores.writingTask2 ? (scores.writingTask1 + scores.writingTask2) / 2 : null,\n                speakingBandScore: scores.speaking,\n                overallBandScore,\n                status: 'completed'\n            };\n            const url = candidate.hasResult ? \"/api/checker/results/\".concat((_candidate_result = candidate.result) === null || _candidate_result === void 0 ? void 0 : _candidate_result.id) : '/api/checker/results';\n            const method = candidate.hasResult ? 'PUT' : 'POST';\n            const response = await fetch(url, {\n                method,\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(resultData)\n            });\n            if (response.ok) {\n                setSuccessMessage(\"\".concat(skill, \" score saved for \").concat(candidate.candidateNumber));\n                setTimeout(()=>setSuccessMessage(''), 3000);\n                await fetchCandidates(); // Refresh data\n            } else {\n                const errorData = await response.json();\n                setError(errorData.error || 'Failed to save score');\n            }\n        } catch (error) {\n            console.error('Error saving score:', error);\n            setError('An error occurred while saving the score');\n        } finally{\n            setIsSaving(false);\n        }\n    };\n    const getBandScoreColor = (score)=>{\n        if (score === null) return 'bg-gray-100 text-gray-400';\n        if (score >= 8.5) return 'bg-green-100 text-green-800';\n        if (score >= 7.0) return 'bg-blue-100 text-blue-800';\n        if (score >= 6.0) return 'bg-yellow-100 text-yellow-800';\n        if (score >= 5.0) return 'bg-orange-100 text-orange-800';\n        return 'bg-red-100 text-red-800';\n    };\n    if (status === 'loading' || isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                lineNumber: 192,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n            lineNumber: 191,\n            columnNumber: 7\n        }, this);\n    }\n    if (!session) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: \"Results Entry\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Quickly assign band scores to candidates\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"h-4 w-4 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 11\n                    }, this),\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                lineNumber: 211,\n                columnNumber: 9\n            }, this),\n            successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-md text-sm flex items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"h-4 w-4 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 11\n                    }, this),\n                    successMessage\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                lineNumber: 218,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white shadow rounded-lg overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"overflow-x-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: \"min-w-full divide-y divide-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                className: \"bg-gray-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Candidate\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Listening\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Reading\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Writing Task 1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Writing Task 2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Speaking\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            className: \"px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                            children: \"Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                className: \"bg-white divide-y divide-gray-200\",\n                                children: candidates.map((candidate)=>{\n                                    const scores = bandScores[candidate.id] || {};\n                                    const overallScore = calculateOverallBandScore(scores);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        className: \"hover:bg-gray-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-shrink-0 h-10 w-10\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium text-blue-800\",\n                                                                    children: candidate.candidateNumber\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                                    lineNumber: 264,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                                lineNumber: 263,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"ml-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium text-gray-900\",\n                                                                    children: candidate.fullName\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                                    lineNumber: 270,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: [\n                                                                        \"Test Date: \",\n                                                                        new Date(candidate.testDate).toLocaleDateString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                                    lineNumber: 273,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                overallScore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-400\",\n                                                                    children: [\n                                                                        \"Overall: \",\n                                                                        overallScore\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                                    lineNumber: 277,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BandScoreSelector, {\n                                                    value: scores.listening,\n                                                    onChange: (score)=>updateBandScore(candidate.id, 'listening', score),\n                                                    onSave: ()=>saveBandScore(candidate.id, 'listening'),\n                                                    disabled: isSaving\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BandScoreSelector, {\n                                                    value: scores.reading,\n                                                    onChange: (score)=>updateBandScore(candidate.id, 'reading', score),\n                                                    onSave: ()=>saveBandScore(candidate.id, 'reading'),\n                                                    disabled: isSaving\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BandScoreSelector, {\n                                                    value: scores.writingTask1,\n                                                    onChange: (score)=>updateBandScore(candidate.id, 'writingTask1', score),\n                                                    onSave: ()=>saveBandScore(candidate.id, 'writingTask1'),\n                                                    disabled: isSaving\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BandScoreSelector, {\n                                                    value: scores.writingTask2,\n                                                    onChange: (score)=>updateBandScore(candidate.id, 'writingTask2', score),\n                                                    onSave: ()=>saveBandScore(candidate.id, 'writingTask2'),\n                                                    disabled: isSaving\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BandScoreSelector, {\n                                                    value: scores.speaking,\n                                                    onChange: (score)=>updateBandScore(candidate.id, 'speaking', score),\n                                                    onSave: ()=>saveBandScore(candidate.id, 'speaking'),\n                                                    disabled: isSaving\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                className: \"px-6 py-4 whitespace-nowrap text-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        var _candidate_result;\n                                                        return router.push(\"/dashboard/results/\".concat(((_candidate_result = candidate.result) === null || _candidate_result === void 0 ? void 0 : _candidate_result.id) || candidate.id));\n                                                    },\n                                                    className: \"text-blue-600 hover:text-blue-900 text-sm\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Eye_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, candidate.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                    lineNumber: 226,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                lineNumber: 225,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n        lineNumber: 202,\n        columnNumber: 5\n    }, this);\n}\n_s(ResultsEntryPage, \"Cnpn/yrpDs3IaZaLQnTux4eSV2M=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession\n    ];\n});\n_c = ResultsEntryPage;\nfunction BandScoreSelector(param) {\n    let { value, onChange, onSave, disabled } = param;\n    _s1();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const getBandScoreColor = (score)=>{\n        if (score === null) return 'bg-gray-100 text-gray-400 border-gray-200';\n        if (score >= 8.5) return 'bg-green-100 text-green-800 border-green-200';\n        if (score >= 7.0) return 'bg-blue-100 text-blue-800 border-blue-200';\n        if (score >= 6.0) return 'bg-yellow-100 text-yellow-800 border-yellow-200';\n        if (score >= 5.0) return 'bg-orange-100 text-orange-800 border-orange-200';\n        return 'bg-red-100 text-red-800 border-red-200';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"inline-flex items-center px-3 py-2 border rounded-md text-sm font-medium \".concat(getBandScoreColor(value), \" hover:opacity-80 focus:outline-none focus:ring-2 focus:ring-blue-500\"),\n                disabled: disabled,\n                children: value !== null ? \"Band \".concat(value) : 'Pending'\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                lineNumber: 376,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute z-10 mt-1 w-32 bg-white border border-gray-200 rounded-md shadow-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"py-1 max-h-48 overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                onChange(null);\n                                setIsOpen(false);\n                            },\n                            className: \"block w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-100\",\n                            children: \"Pending\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                            lineNumber: 387,\n                            columnNumber: 13\n                        }, this),\n                        BAND_SCORES.map((score)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    onChange(score);\n                                    setIsOpen(false);\n                                    setTimeout(onSave, 100); // Auto-save after selection\n                                },\n                                className: \"block w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-100\",\n                                children: [\n                                    \"Band \",\n                                    score\n                                ]\n                            }, score, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 15\n                            }, this))\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                    lineNumber: 386,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n                lineNumber: 385,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\dashboard\\\\results\\\\entry\\\\page.tsx\",\n        lineNumber: 375,\n        columnNumber: 5\n    }, this);\n}\n_s1(BandScoreSelector, \"+sus0Lb0ewKHdwiUhiTAJFoFyQ0=\");\n_c1 = BandScoreSelector;\nvar _c, _c1;\n$RefreshReg$(_c, \"ResultsEntryPage\");\n$RefreshReg$(_c1, \"BandScoreSelector\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/results/entry/page.tsx\n"));

/***/ })

});